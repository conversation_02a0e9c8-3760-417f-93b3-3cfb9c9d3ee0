@import "tailwindcss";


/* Global overflow hidden to prevent horizontal scrolling */
html, body {
  overflow-x: hidden;
  max-width: 100vw;
}

* {
  box-sizing: border-box;
}

/* About Us Background Image Styling */
.about-hero-bg {
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  background-attachment: fixed;
  min-height: 100vh;
  position: relative;
}

/* Ensure background image is always centered and covers properly */
.about-hero-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: inherit;
  background-position: center center;
  background-size: cover;
  z-index: -1;
}

/* Responsive background image adjustments */
@media (max-width: 768px) {
  .about-hero-bg {
    background-attachment: scroll;
    background-size: cover;
    background-position: center center;
  }
}

.nav-underline {
  display: block;
  position: absolute;
  left: 0;
  bottom: 0;
  height: 2px;
  width: 100%;
  background: #3E1492;
  transform: scaleX(0);
  transform-origin: left;
  border-radius: 2px;
  transition: transform 0.35s cubic-bezier(0.4,0,0.2,1);
}
.group:hover .nav-underline {
  transform: scaleX(1);
}

.Hero{
  background-image: url("../public//Background.jpg");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  height: 835px;
  width: 100%;
  position: relative;
  border-bottom-left-radius: 30px;
  border-bottom-right-radius: 30px;

}

/* Updated About section with proper background positioning */
.about {
  background-image: url("../src/assets/images/aboutUs.png");
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  height: 650px;
  width: 100%;
  position: relative;
  overflow: hidden;
}

/* Alternative background images for different sections */
.about-background {
  background-image: url("../src/assets/images/aboutUs.png");
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  min-height: 90vh;
  position: relative;
  overflow: hidden;
}

/* Mobile background image change */
@media (max-width: 768px) {
  .about-background {
    background-image: url("../src/assets/images/Background.jpg");
    background-attachment: scroll;
    min-height: 100vh;
  }
}

.about-grid {
  background-image: url("../src/assets/images/AboutUsGrid.jpg");
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

/* Navbar Mobile Menu - No Scroll */
.navbar-mobile-menu {
  overflow: hidden !important;
  max-width: 100vw !important;
  width: 100% !important;
  height: 100vh !important;
  max-height: 100vh !important;
}

.navbar-mobile-menu * {
  overflow-x: hidden !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

/* Mobile Menu Content Constraints */
.mobile-menu-content {
  height: 100vh !important;
  max-height: 100vh !important;
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  align-items: center !important;
}

.mobile-menu-nav {
  max-height: calc(100vh - 160px) !important;
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
}

/* Prevent horizontal scroll on mobile */
@media (max-width: 1024px) {
  header {
    overflow-x: hidden !important;
    max-width: 100vw !important;
  }

  nav {
    overflow-x: hidden !important;
    max-width: 100% !important;
  }

  .mobile-menu-item {
    overflow-x: hidden !important;
    word-wrap: break-word !important;
    max-width: 100% !important;
  }

  /* Ensure mobile menu fits in viewport */
  .mobile-menu-text {
    font-size: clamp(1.5rem, 4vw, 2rem) !important;
    line-height: 1.2 !important;
  }
}
