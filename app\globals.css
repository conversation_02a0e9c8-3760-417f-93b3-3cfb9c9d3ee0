@import "tailwindcss";

/* Global overflow hidden to prevent horizontal scrolling */
html, body {
  overflow-x: hidden;
  max-width: 100vw;
}

* {
  box-sizing: border-box;
}

/* About Us Background Image Styling */
.about-hero-bg {
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  background-attachment: fixed;
  min-height: 100vh;
  position: relative;
}

/* Ensure background image is always centered and covers properly */
.about-hero-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: inherit;
  background-position: center center;
  background-size: cover;
  z-index: -1;
}

/* Responsive background image adjustments */
@media (max-width: 768px) {
  .about-hero-bg {
    background-attachment: scroll;
    background-size: cover;
    background-position: center center;
  }
}

.nav-underline {
  display: block;
  position: absolute;
  left: 0;
  bottom: 0;
  height: 2px;
  width: 100%;
  background: #3E1492;
  transform: scaleX(0);
  transform-origin: left;
  border-radius: 2px;
  transition: transform 0.35s cubic-bezier(0.4,0,0.2,1);
}
.group:hover .nav-underline {
  transform: scaleX(1);
}

.Hero{
  background-image: url("../public//Background.jpg");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  height: 835px;
  width: 100%;
  position: relative;
  border-bottom-left-radius: 30px;
  border-bottom-right-radius: 30px;

}

/* Updated About section with proper background positioning */
.about {
  background-image: url("../src/assets/images/aboutUs.png");
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  height: 650px;
  width: 100%;
  position: relative;
  overflow: hidden;
}

/* Alternative background images for different sections */
.about-background {
  background-image: url("../src/assets/images/Background.jpg");
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

.about-grid {
  background-image: url("../src/assets/images/AboutUsGrid.jpg");
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

/* Pricing Section - No Scroll */
#pricing {
  overflow-x: hidden !important;
  width: 100%;
  max-width: 100vw;
}

#pricing * {
  box-sizing: border-box;
}

/* Ensure pricing cards don't cause overflow */
.pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 100%;
  overflow: hidden;
}

/* Responsive pricing cards */
@media (max-width: 1024px) {
  .pricing-grid {
    grid-template-columns: 1fr;
    place-items: center;
  }
}

@media (min-width: 1025px) {
  .pricing-grid {
    grid-template-columns: repeat(3, 1fr);
    max-width: 1200px;
    margin: 0 auto;
  }
}
