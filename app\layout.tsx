import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON>, <PERSON><PERSON> } from "next/font/google";

import Footer from "@/components/Footer/Footer";
import Navbar from "@/components/Navbar/Navbar";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});
const roboto = Roboto({
  weight: ["400", "500", "700"], // Add weights you need
  subsets: ["latin"],
  variable: "--font-roboto",
});

export const metadata: Metadata = {
  title: "<PERSON><PERSON><PERSON> Fran<PERSON>",
  description:
    "<PERSON><PERSON>fe Franca is a leading fitness platform offering expert trainers, personalized programs, state-of-the-art equipment, and a supportive community. Achieve your health and wellness goals with our comprehensive classes and professional guidance. Perfect for all fitness levels.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${geistSans.variable} ${geistMono.variable} ${roboto.variable} antialiased`}>
        <Navbar />
        <main className=" flex flex-col items-center justify-center md:block">
          {children}
        </main>
        <Footer />

      </body>
    </html>
  );
}
