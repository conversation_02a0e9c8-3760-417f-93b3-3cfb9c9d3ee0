"use client";

import Side_menu from "@/public//Side_Menu_Mockup.png";
import login from "@/public/Login_Mockup.png";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import Image from "next/image";
import { useEffect, useRef } from "react";

// Register GSAP plugins
if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

export default function Hero() {
  const heroRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const buttonsRef = useRef<HTMLDivElement>(null);
  const imageContainerRef = useRef<HTMLDivElement>(null);
  const sideMenuRef = useRef<HTMLImageElement>(null);
  const loginRef = useRef<HTMLImageElement>(null);

  useEffect(() => {
    const initAnimations = () => {
      const hero = heroRef.current;
      const content = contentRef.current;
      const title = titleRef.current;
      const subtitle = subtitleRef.current;
      const buttons = buttonsRef.current;
      const imageContainer = imageContainerRef.current;
      const sideMenu = sideMenuRef.current;
      const login = loginRef.current;

      if (
        !hero ||
        !content ||
        !title ||
        !subtitle ||
        !buttons ||
        !imageContainer ||
        !sideMenu ||
        !login
      ) {
        // Retry after a short delay if elements aren't ready
        setTimeout(initAnimations, 50);
        return;
      }

      // Set initial states to prevent flash of unstyled content
      gsap.set(hero, { opacity: 0, scale: 1.1 });
      gsap.set(title, { opacity: 0, y: 50 });
      gsap.set(subtitle, { opacity: 0, x: -50 });
      gsap.set(buttons.children, { opacity: 0, y: 30, scale: 0.8 });
      gsap.set(login, { opacity: 0, x: 100, rotation: 10 });
      gsap.set(sideMenu, { opacity: 0, x: 50, y: -20 });

      // Create timeline with a small delay to ensure everything is ready
      const tl = gsap.timeline({ delay: 0.2 });

      // Hero section background animation
      tl.to(hero, { opacity: 1, scale: 1, duration: 1.2, ease: "power2.out" })
        // Title animation with typewriter effect
        .to(title, { opacity: 1, y: 0, duration: 0.8, ease: "back.out(1.7)" }, "-=0.8")
        // Subtitle slides in from left
        .to(subtitle, { opacity: 1, x: 0, duration: 0.6, ease: "power2.out" }, "-=0.4")
        // Buttons animate in with bounce
        .to(
          buttons.children,
          { opacity: 1, y: 0, scale: 1, duration: 0.5, stagger: 0.2, ease: "back.out(1.7)" },
          "-=0.3",
        )
        // Images animate in with creative effects
        .to(login, { opacity: 1, x: 0, rotation: 0, duration: 0.8, ease: "back.out(1.7)" }, "-=0.6")
        .to(sideMenu, { opacity: 1, x: 0, y: 0, duration: 0.8, ease: "power2.out" }, "-=0.4");

      // Scroll-triggered animations
      ScrollTrigger.create({
        trigger: hero,
        start: "top 80%",
        end: "bottom 20%",
        onUpdate: self => {
          const progress = self.progress;
          // Parallax effect for images
          gsap.to(sideMenu, { y: progress * 30, duration: 0.3 });
          gsap.to(login, { y: progress * -20, duration: 0.3 });
        },
      });

      // Floating animation for images
      gsap.to(login, {
        y: "+=10",
        duration: 2,
        ease: "power1.inOut",
        yoyo: true,
        repeat: -1,
      });

      gsap.to(sideMenu, {
        y: "+=15",
        rotation: "+=2",
        duration: 3,
        ease: "power1.inOut",
        yoyo: true,
        repeat: -1,
        delay: 0.5,
      });

      // Button hover animations
      const downloadBtn = buttons.children[0] as HTMLElement;
      const webBtn = buttons.children[1] as HTMLElement;

      if (downloadBtn) {
        downloadBtn.addEventListener("mouseenter", () => {
          gsap.to(downloadBtn, {
            scale: 1.05,
            boxShadow: "0 15px 35px rgba(62, 20, 146, 0.4)",
            duration: 0.3,
            ease: "power2.out",
          });
        });

        downloadBtn.addEventListener("mouseleave", () => {
          gsap.to(downloadBtn, {
            scale: 1,
            boxShadow: "0 0 0 rgba(62, 20, 146, 0)",
            duration: 0.3,
            ease: "power2.out",
          });
        });
      }

      if (webBtn) {
        webBtn.addEventListener("mouseenter", () => {
          gsap.to(webBtn, {
            scale: 1.05,
            borderColor: "#FCFCFD",
            backgroundColor: "rgba(252, 252, 253, 0.1)",
            duration: 0.3,
            ease: "power2.out",
          });
        });

        webBtn.addEventListener("mouseleave", () => {
          gsap.to(webBtn, {
            scale: 1,
            borderColor: "#3E1492",
            backgroundColor: "transparent",
            duration: 0.3,
            ease: "power2.out",
          });
        });
      }

      return () => {
        ScrollTrigger.getAll().forEach(trigger => trigger.kill());
      };
    };

    // Start initialization
    initAnimations();
  }, []);

  return (
    <div
      ref={heroRef}
      className="Hero bg-gradient-to-r from-[#1a1442] to-[#3E1492] py-10 md:py-20 rounded-b-3xl overflow-hidden px-4"
    >
      <div className="flex flex-col items-center justify-center min-h-[80vh] px-4 md:grid md:grid-cols-2 md:place-items-center md:gap-10 md:max-w-[1140px] md:mx-auto md:px-0">
        {/* content */}
        <div
          ref={contentRef}
          className="text-[#FCFCFD] flex flex-col space-y-6 max-w-full  md:max-w-[400px] lg:max-w-[558px]   "
        >
          <h1
            ref={titleRef}
            className="font-bold  text-2xl md:text-3xl lg:text-5xl max-w-lg leading-[150%]"
          >
            Get Fit. Pay Less.
            <br />
            Stay Strong with
            <br /> Shrife Franca App
          </h1>
          <p ref={subtitleRef} className="text-base md:text-lg max-w-sm">
            Join Shrife Franca Platform Today and, Personalized Programs, and a Supportive Community
            to Achieve Your Fitness Goals.
          </p>
          <div ref={buttonsRef} className="flex flex-col lg:flex-row gap-4">
            <button className="bg-[#3E1492] text-white px-4 py-2 rounded-md cursor-pointer transition-all duration-300">
              Download App Now
            </button>
            <button className="cursor-pointer border border-[#3E1492] text-[#FCFCFD] px-4 py-2 rounded-md transition-all duration-300">
              Use Web Platform
            </button>
          </div>
        </div>
        {/* image */}
        <div
          ref={imageContainerRef}
          className="hidden md:block relative w-[220px] h-[450px] md:w-[350px] md:h-[725px] mx-auto mt-8 md:mt-0"
        >
          <Image
            ref={sideMenuRef}
            src={Side_menu}
            alt="side menu"
            width={350}
            height={725}
            className="absolute top-8 left-40 z-0 w-full h-full object-contain"
          />
          <Image
            ref={loginRef}
            src={login}
            alt="login"
            width={350}
            height={725}
            className="relative z-10 w-full h-full object-contain"
          />
        </div>
      </div>
    </div>
  );
}
