"use client"

import Side_menu from "@/public//Side_Menu_Mockup.png";
import login from "@/public/Login_Mockup.png";
import { useGSAP } from "@gsap/react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { TextPlugin } from "gsap/TextPlugin";
import Image from "next/image";
import { useRef } from "react";

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger, TextPlugin, useGSAP);

export default function Hero() {
  const heroRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const buttonsRef = useRef<HTMLDivElement>(null);
  const imageContainerRef = useRef<HTMLDivElement>(null);
  const sideMenuRef = useRef<HTMLImageElement>(null);
  const loginRef = useRef<HTMLImageElement>(null);

  // 🚀 HERO - Cinematic Entrance Animations
  useGSAP(
    () => {
      const hero = heroRef.current;
      const content = contentRef.current;
      const title = titleRef.current;
      const subtitle = subtitleRef.current;
      const buttons = buttonsRef.current;
      const imageContainer = imageContainerRef.current;
      const sideMenu = sideMenuRef.current;
      const login = loginRef.current;

      if (
        !hero ||
        !content ||
        !title ||
        !subtitle ||
        !buttons ||
        !imageContainer ||
        !sideMenu ||
        !login
      )
        return;

      // 🌟 Scroll-triggered cinematic entrance
      ScrollTrigger.create({
        trigger: hero,
        start: "top 80%",
        end: "bottom 20%",
        onEnter: () => {
          const masterTL = gsap.timeline();

          // Hero background morphs in with gradient shift
          masterTL
            .fromTo(
              hero,
              {
                opacity: 0,
                scale: 1.2,
                filter: "blur(20px)",
                background: "linear-gradient(to right, #000000, #000000)",
              },
              {
                opacity: 1,
                scale: 1,
                filter: "blur(0px)",
                background: "linear-gradient(to right, #1a1442, #3E1492)",
                duration: 1.5,
                ease: "power3.out",
              },
            )
            // Title appears with typewriter + 3D flip effect
            .fromTo(
              title,
              {
                opacity: 0,
                y: 100,
                rotationX: -90,
                transformOrigin: "50% 100%",
                filter: "brightness(0)",
              },
              {
                opacity: 1,
                y: 0,
                rotationX: 0,
                filter: "brightness(1)",
                duration: 1.2,
                ease: "back.out(1.7)",
              },
              "-=1",
            )
            // Subtitle slides with particle effect
            .fromTo(
              subtitle,
              {
                opacity: 0,
                x: -100,
                filter: "blur(10px)",
                scale: 0.8,
              },
              {
                opacity: 1,
                x: 0,
                filter: "blur(0px)",
                scale: 1,
                duration: 0.8,
                ease: "power2.out",
              },
              "-=0.8",
            )
            // Buttons materialize with energy burst
            .fromTo(
              buttons.children,
              {
                opacity: 0,
                scale: 0,
                rotation: 180,
                filter: "brightness(0)",
                y: 50,
              },
              {
                opacity: 1,
                scale: 1,
                rotation: 0,
                filter: "brightness(1)",
                y: 0,
                duration: 0.8,
                stagger: 0.2,
                ease: "elastic.out(1, 0.8)",
              },
              "-=0.6",
            )
            // Images enter with 3D transformation
            .fromTo(
              login,
              {
                opacity: 0,
                x: 200,
                rotationY: 90,
                scale: 0.5,
                filter: "drop-shadow(0 0 0 rgba(62, 20, 146, 0))",
              },
              {
                opacity: 1,
                x: 0,
                rotationY: 0,
                scale: 1,
                filter: "drop-shadow(0 20px 40px rgba(62, 20, 146, 0.3))",
                duration: 1.2,
                ease: "power3.out",
              },
              "-=0.8",
            )
            .fromTo(
              sideMenu,
              {
                opacity: 0,
                x: 150,
                y: -50,
                rotation: 45,
                scale: 0.3,
              },
              {
                opacity: 1,
                x: 0,
                y: 0,
                rotation: 0,
                scale: 1,
                duration: 1,
                ease: "back.out(1.7)",
              },
              "-=0.6",
            );
        },
      });

      // 🎯 Advanced scroll-triggered parallax & morphing
      ScrollTrigger.create({
        trigger: hero,
        start: "top bottom",
        end: "bottom top",
        scrub: 1,
        onUpdate: self => {
          const progress = self.progress;

          // Dynamic parallax with depth
          gsap.to(sideMenu, {
            y: progress * 80,
            rotation: progress * 10,
            scale: 1 + progress * 0.1,
            duration: 0.3,
          });

          gsap.to(login, {
            y: progress * -60,
            rotationY: progress * 15,
            duration: 0.3,
          });

          // Background color shift based on scroll
          gsap.to(hero, {
            background: `linear-gradient(to right,
            hsl(${240 + progress * 20}, 70%, ${20 + progress * 10}%),
            hsl(${260 + progress * 30}, 80%, ${30 + progress * 15}%))`,
            duration: 0.3,
          });
        },
      });

      // 🌊 Continuous floating animations with physics
      gsap.to(login, {
        y: "+=20",
        rotation: "+=3",
        duration: 4,
        ease: "sine.inOut",
        yoyo: true,
        repeat: -1,
      });

      gsap.to(sideMenu, {
        y: "+=15",
        x: "+=5",
        rotation: "+=2",
        duration: 5,
        ease: "sine.inOut",
        yoyo: true,
        repeat: -1,
        delay: 1,
      });
    },
    { scope: heroRef },
  );
  return (
    <div
      ref={heroRef}
      className="Hero bg-gradient-to-r from-[#1a1442] to-[#3E1492] py-10 md:py-20 rounded-b-3xl overflow-hidden px-4 "
    >
      <div className="flex flex-col items-center justify-center min-h-[80vh] px-4 md:grid md:grid-cols-2 md:place-items-center md:gap-10 md:max-w-[1140px] md:mx-auto md:px-0">
        {/* content */}
        <div
          ref={contentRef}
          className="text-[#FCFCFD] flex flex-col space-y-6 max-w-full  md:max-w-[400px] lg:max-w-[558px]   "
        >
          <h1
            ref={titleRef}
            className="font-bold  text-2xl md:text-3xl lg:text-5xl max-w-lg leading-tight"
          >
            Get Fit. Pay Less.
            <br />
            Stay Strong with
            <br /> Shrife Franca App
          </h1>
          <p ref={subtitleRef} className="text-base md:text-lg max-w-sm">
            Join Shrife Franca Platform Today and, Personalized Programs, and a Supportive Community
            to Achieve Your Fitness Goals.
          </p>
          <div ref={buttonsRef} className="flex flex-col lg:flex-row gap-4">
            <button className="bg-[#3E1492] text-white px-4 py-2 rounded-md cursor-pointer transition-all duration-300 hover:bg-[#2D0F6B] hover:scale-105">
              Download App Now
            </button>
            <button className="cursor-pointer border border-[#3E1492] text-[#FCFCFD] px-4 py-2 rounded-md transition-all duration-300 hover:bg-[#3E1492] hover:scale-105">
              Use Web Platform
            </button>
          </div>
        </div>
        {/* image */}
        <div
          ref={imageContainerRef}
          className="hidden md:block relative w-[220px] h-[450px] md:w-[350px] md:h-[725px] mx-auto mt-8 md:mt-0"
        >
          <Image
            ref={sideMenuRef}
            src={Side_menu}
            alt="side menu"
            width={350}
            height={725}
            className="absolute top-8 left-40 z-0 w-full h-full object-contain"
          />
          <Image
            ref={loginRef}
            src={login}
            alt="login"
            width={350}
            height={725}
            className="relative z-10 w-full h-full object-contain"
          />
        </div>
      </div>
    </div>
  );
}
