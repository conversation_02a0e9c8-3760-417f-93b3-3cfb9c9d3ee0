/* Navbar Animation Styles */
.nav-underline {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #3E1492;
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

.nav-item:hover .nav-underline {
  transform: scaleX(1);
}

/* Additional hover effects */
.nav-item {
  position: relative;
  transition: all 0.3s ease;
}

.nav-item:hover {
  transform: translateY(-2px);
}

/* Mobile menu animations */
.mobile-menu-enter {
  transform: translateX(-100%);
  opacity: 0;
}

.mobile-menu-enter-active {
  transform: translateX(0);
  opacity: 1;
  transition: all 0.4s ease;
}

.mobile-menu-exit {
  transform: translateX(0);
  opacity: 1;
}

.mobile-menu-exit-active {
  transform: translateX(-100%);
  opacity: 0;
  transition: all 0.3s ease;
}

/* Button hover effects */
.join-button {
  transition: all 0.3s ease;
}

.join-button:hover {
  transform: scale(1.05);
  box-shadow: 0 10px 25px rgba(62, 20, 146, 0.3);
}

/* Hero button animations */
.hero-button {
  transition: all 0.3s ease;
}

.hero-button:hover {
  transform: scale(1.05);
}

.download-button:hover {
  box-shadow: 0 15px 35px rgba(62, 20, 146, 0.4);
}

.web-button:hover {
  border-color: #FCFCFD;
  background-color: rgba(252, 252, 253, 0.1);
}
