'use client'

import { NavbarLinks } from "@/data/NavbarLinks";
import logo from "@/public/logo.png";
import { gsap } from "gsap";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useRef, useState } from "react";

export default function Navbar() {
  const [menuOpen, setMenuOpen] = useState(false);
  const navbarRef = useRef<HTMLElement>(null);
  const logoRef = useRef<HTMLDivElement>(null);
  const menuItemsRef = useRef<HTMLUListElement>(null);
  const joinButtonRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    const tl = gsap.timeline();
    tl.from(navbarRef.current, { y: -60, opacity: 0, duration: 0.7, ease: "power3.out" })
      .from(
        logoRef.current,
        { scale: 0.7, rotation: -30, opacity: 0, duration: 0.5, ease: "back.out(1.7)" },
        "-=0.4",
      )
      .from(
        menuItemsRef.current?.children,
        { y: 20, opacity: 0, stagger: 0.1, duration: 0.4, ease: "power2.out" },
        "-=0.3",
      )
      .from(
        joinButtonRef.current,
        { x: 40, opacity: 0, duration: 0.4, ease: "power2.out" },
        "-=0.3",
      );

    if (menuItemsRef.current) {
      Array.from(menuItemsRef.current.children).forEach(li => {
        const link = li.querySelector("a");
        const underline = li.querySelector(".nav-underline");
        if (link && underline) {
          li.addEventListener("mouseenter", () => {
            gsap.to(underline, { scaleX: 1, duration: 0.4, ease: "power2.out" });
          });
          li.addEventListener("mouseleave", () => {
            gsap.to(underline, { scaleX: 0, duration: 0.4, ease: "power2.in" });
          });
        }
      });
    }
  }, []);

  return (
    <header
      ref={navbarRef}
      className="fixed bg-[#FCFCFD] p-4 rounded-b-3xl z-50 shadow-2xl h-[72px] w-full"
    >
      <nav className="flex justify-between items-center max-w-[1140px] mx-auto">
        <div ref={logoRef}>
          <Image src={logo} alt="logo" width={100} height={100} />
        </div>
        {/* Desktop Menu */}
        <ul ref={menuItemsRef} className="hidden lg:flex items-center justify-between gap-10">
          {NavbarLinks.map(link => (
            <li key={link.id} className="group relative hover:text-[#3E1492]">
              <Link href={link.href} className="font-semibold text-xl">
                {link.name}
                <span className="nav-underline"></span>
              </Link>
            </li>
          ))}
        </ul>
        {/* Mobile Hamburger */}
        <button
          className="lg:hidden flex flex-col justify-center items-center w-10 h-10 focus:outline-none"
          onClick={() => setMenuOpen(prev => !prev)}
          aria-label="Toggle menu"
        >
          <span
            className={`block w-6 h-0.5 bg-[#3E1492] mb-1 transition-all ${
              menuOpen ? "rotate-45 translate-y-2" : ""
            }`}
          ></span>
          <span
            className={`block w-6 h-0.5 bg-[#3E1492] mb-1 transition-all ${
              menuOpen ? "opacity-0" : ""
            }`}
          ></span>
          <span
            className={`block w-6 h-0.5 bg-[#3E1492] transition-all ${
              menuOpen ? "-rotate-45 -translate-y-2" : ""
            }`}
          ></span>
        </button>
        <div className="hidden lg:block">
          <button
            ref={joinButtonRef}
            className="bg-[#3E1492] text-white px-4 py-2 rounded-md cursor-pointer"
          >
            Join Now
          </button>
        </div>
      </nav>
      {/* Mobile Menu */}
      <div
        className={`lg:hidden fixed top-0 left-0 w-full h-full bg-[#FCFCFD] z-40 ${
          menuOpen ? "" : "hidden"
        }`}
      >
        <div className="flex justify-between items-center p-4">
          <Image src={logo} alt="logo" width={80} height={80} />
          <button onClick={() => setMenuOpen(false)} aria-label="Close menu">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="#3E1492"
              className="w-8 h-8"
            >
              <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <ul className="flex flex-col px-6 gap-8 mt-10">
          {NavbarLinks.map(link => (
            <li
              key={link.id}
              className="text-[#3E1492] border-b-2 mb-2 text-2xl font-semibold"
              onClick={() => setMenuOpen(false)}
            >
              <Link href={link.href}>{link.name}</Link>
            </li>
          ))}
          <li>
            <button className="bg-[#3E1492] text-white px-6 py-3 rounded-md mt-4 cursor-pointer">
              Join Now
            </button>
          </li>
        </ul>
      </div>
    </header>
  );
}
