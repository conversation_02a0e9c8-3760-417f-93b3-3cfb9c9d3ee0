'use client'

import { NavbarLinks } from "@/data/NavbarLinks";
import logo from "@/public/logo.png";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useRef, useState } from "react";

// Register GSAP plugins
if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

export default function Navbar() {
  const [menuOpen, setMenuOpen] = useState(false);
  const navbarRef = useRef<HTMLElement>(null);
  const logoRef = useRef<HTMLDivElement>(null);
  const menuItemsRef = useRef<HTMLUListElement>(null);
  const joinButtonRef = useRef<HTMLButtonElement>(null);
  const mobileMenuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const navbar = navbarRef.current;
    const logo = logoRef.current;
    const menuItems = menuItemsRef.current;
    const joinButton = joinButtonRef.current;

    if (!navbar || !logo || !menuItems || !joinButton) return;

    // Initial animation on page load
    const tl = gsap.timeline();

    // Navbar slides down with a bounce
    tl.fromTo(
      navbar,
      { y: -100, opacity: 0 },
      { y: 0, opacity: 1, duration: 0.8, ease: "back.out(1.7)" },
    )
      // Logo scales in with rotation
      .fromTo(
        logo,
        { scale: 0, rotation: -180 },
        { scale: 1, rotation: 0, duration: 0.6, ease: "back.out(1.7)" },
        "-=0.4",
      )
      // Menu items animate in from left with stagger
      .fromTo(
        menuItems.children,
        { x: -50, opacity: 0 },
        { x: 0, opacity: 1, duration: 0.5, stagger: 0.1, ease: "power2.out" },
        "-=0.3",
      )
      // Join button slides in from right
      .fromTo(
        joinButton,
        { x: 50, opacity: 0 },
        { x: 0, opacity: 1, duration: 0.5, ease: "power2.out" },
        "-=0.2",
      );

    // Scroll-triggered animations
    ScrollTrigger.create({
      trigger: "body",
      start: "top top",
      end: "bottom bottom",
      onUpdate: self => {
        const velocity = self.getVelocity();
        if (velocity < -300) {
          // Scrolling down fast - hide navbar
          gsap.to(navbar, { y: -100, duration: 0.3, ease: "power2.out" });
        } else if (velocity > 300) {
          // Scrolling up fast - show navbar
          gsap.to(navbar, { y: 0, duration: 0.3, ease: "power2.out" });
        }
      },
    });

    // Hover animations for menu items
    const menuItemElements = menuItems.querySelectorAll("li");
    menuItemElements.forEach(item => {
      const link = item.querySelector("a");
      const underline = item.querySelector(".nav-underline");

      if (link && underline) {
        item.addEventListener("mouseenter", () => {
          gsap.to(link, { y: -2, duration: 0.2, ease: "power2.out" });
          gsap.to(underline, { scaleX: 1, duration: 0.3, ease: "power2.out" });
        });

        item.addEventListener("mouseleave", () => {
          gsap.to(link, { y: 0, duration: 0.2, ease: "power2.out" });
          gsap.to(underline, { scaleX: 0, duration: 0.3, ease: "power2.out" });
        });
      }
    });

    // Join button hover animation
    joinButton.addEventListener("mouseenter", () => {
      gsap.to(joinButton, {
        scale: 1.05,
        boxShadow: "0 10px 25px rgba(62, 20, 146, 0.3)",
        duration: 0.3,
        ease: "power2.out",
      });
    });

    joinButton.addEventListener("mouseleave", () => {
      gsap.to(joinButton, {
        scale: 1,
        boxShadow: "0 0 0 rgba(62, 20, 146, 0)",
        duration: 0.3,
        ease: "power2.out",
      });
    });

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  // Mobile menu animation
  useEffect(() => {
    const mobileMenu = mobileMenuRef.current;
    if (!mobileMenu) return;

    if (menuOpen) {
      gsap.set(mobileMenu, { display: "block" });
      gsap.fromTo(
        mobileMenu,
        { x: "-100%", opacity: 0 },
        { x: "0%", opacity: 1, duration: 0.4, ease: "power2.out" },
      );

      // Animate mobile menu items
      const mobileMenuItems = mobileMenu.querySelectorAll("li");
      gsap.fromTo(
        mobileMenuItems,
        { x: -30, opacity: 0 },
        { x: 0, opacity: 1, duration: 0.3, stagger: 0.1, delay: 0.2, ease: "power2.out" },
      );
    } else {
      gsap.to(mobileMenu, {
        x: "-100%",
        opacity: 0,
        duration: 0.3,
        ease: "power2.in",
        onComplete: () => {
          gsap.set(mobileMenu, { display: "none" });
        },
      });
    }
  }, [menuOpen]);

  return (
    <header
      ref={navbarRef}
      className=" fixed bg-[#FCFCFD] p-4 rounded-b-3xl z-50 shadow-2xl h-[72px] w-full"
    >
      <nav className="flex justify-between items-center max-w-[1140px] mx-auto">
        <div ref={logoRef}>
          <Image src={logo} alt="logo" width={100} height={100} />
        </div>
        {/* Desktop Menu */}
        <ul ref={menuItemsRef} className="hidden lg:flex items-center justify-between gap-10">
          {NavbarLinks.map(link => (
            <li key={link.id} className="group relative hover:text-[#3E1492]">
              <Link href={link.href} className="font-semibold text-xl">
                {link.name}
                <span className="nav-underline absolute bottom-0 left-0 w-full h-0.5 bg-[#3E1492] transform scale-x-0 origin-left"></span>
              </Link>
            </li>
          ))}
        </ul>
        {/* Mobile Hamburger */}
        <button
          className="lg:hidden flex flex-col justify-center items-center w-10 h-10 focus:outline-none"
          onClick={() => setMenuOpen(prev => !prev)}
          aria-label="Toggle menu"
        >
          <span
            className={`block w-6 h-0.5 bg-[#3E1492] mb-1 transition-all ${
              menuOpen ? "rotate-45 translate-y-2" : ""
            }`}
          ></span>
          <span
            className={`block w-6 h-0.5 bg-[#3E1492] mb-1 transition-all ${
              menuOpen ? "opacity-0" : ""
            }`}
          ></span>
          <span
            className={`block w-6 h-0.5 bg-[#3E1492] transition-all ${
              menuOpen ? "-rotate-45 -translate-y-2" : ""
            }`}
          ></span>
        </button>
        <div className="hidden lg:block">
          <button
            ref={joinButtonRef}
            className="bg-[#3E1492] text-white px-4 py-2 rounded-md cursor-pointer"
          >
            Join Now
          </button>
        </div>
      </nav>
      {/* Mobile Menu */}
      <div
        ref={mobileMenuRef}
        className="lg:hidden fixed top-0 left-0 w-full h-full bg-[#FCFCFD] z-40 hidden"
      >
        <div className="flex justify-between items-center p-4">
          <Image src={logo} alt="logo" width={80} height={80} />
          <button onClick={() => setMenuOpen(false)} aria-label="Close menu">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="#3E1492"
              className="w-8 h-8"
            >
              <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <ul className="flex flex-col px-6  gap-8 mt-10">
          {NavbarLinks.map(link => (
            <li
              key={link.id}
              className="text-[#3E1492] border-b-2 mb-2 text-2xl font-semibold"
              onClick={() => setMenuOpen(false)}
            >
              <Link href={link.href}>{link.name}</Link>
            </li>
          ))}
          <li>
            <button className="bg-[#3E1492] text-white px-6 py-3 rounded-md mt-4 cursor-pointer">
              Join Now
            </button>
          </li>
        </ul>
      </div>
    </header>
  );
}
