"use client";

import platform from "@/public/platform.jpg";
import { useGSAP } from "@gsap/react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { CheckCircle2, ChevronRight } from "lucide-react";
import Image from "next/image";
import { useRef } from "react";
import FlexComponent from "./FlexComponent";

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger, useGSAP);

export default function Platform() {
  const platformRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const featuresRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  // 🎨 PLATFORM SECTION - Liquid Morphing Animations
  useGSAP(
    () => {
      const platform = platformRef.current;
      const image = imageRef.current;
      const content = contentRef.current;
      const title = titleRef.current;
      const subtitle = subtitleRef.current;
      const features = featuresRef.current;
      const button = buttonRef.current;

      if (!platform || !image || !content || !title || !subtitle || !features || !button) return;

      // 🌊 Liquid entrance animation on scroll
      ScrollTrigger.create({
        trigger: platform,
        start: "top 80%",
        end: "bottom 20%",
        onEnter: () => {
          const tl = gsap.timeline();

          // Image morphs in with liquid effect
          tl.fromTo(
            image,
            {
              scale: 0.3,
              rotation: -15,
              opacity: 0,
              filter: "blur(20px)",
              borderRadius: "50%",
            },
            {
              scale: 1,
              rotation: 0,
              opacity: 1,
              filter: "blur(0px)",
              borderRadius: "24px",
              duration: 1.2,
              ease: "elastic.out(1, 0.8)",
            },
          )
            // Title slides with wave effect
            .fromTo(
              title,
              {
                x: -100,
                opacity: 0,
                rotationY: -45,
                transformOrigin: "left center",
              },
              {
                x: 0,
                opacity: 1,
                rotationY: 0,
                duration: 0.8,
                ease: "power3.out",
              },
              "-=0.8",
            )
            // Subtitle fades with typewriter
            .fromTo(
              subtitle,
              {
                opacity: 0,
                y: 30,
                filter: "blur(5px)",
              },
              {
                opacity: 1,
                y: 0,
                filter: "blur(0px)",
                duration: 0.6,
                ease: "power2.out",
              },
              "-=0.4",
            )
            // Features cascade with bounce
            .fromTo(
              features.children,
              {
                x: -50,
                opacity: 0,
                scale: 0.8,
                rotation: -5,
              },
              {
                x: 0,
                opacity: 1,
                scale: 1,
                rotation: 0,
                duration: 0.6,
                stagger: 0.15,
                ease: "back.out(1.7)",
              },
              "-=0.3",
            )
            // Button materializes with glow
            .fromTo(
              button,
              {
                scale: 0,
                opacity: 0,
                boxShadow: "0 0 0 rgba(62, 20, 146, 0)",
              },
              {
                scale: 1,
                opacity: 1,
                boxShadow: "0 10px 30px rgba(62, 20, 146, 0.3)",
                duration: 0.7,
                ease: "elastic.out(1, 0.6)",
              },
              "-=0.2",
            );
        },
      });

      // 🎯 Continuous parallax on scroll
      ScrollTrigger.create({
        trigger: platform,
        start: "top bottom",
        end: "bottom top",
        scrub: 1,
        onUpdate: self => {
          const progress = self.progress;

          // Image parallax with rotation
          gsap.to(image, {
            y: progress * -50,
            rotation: progress * 5,
            scale: 1 + progress * 0.1,
            duration: 0.3,
          });

          // Content counter-parallax
          gsap.to(content, {
            y: progress * 30,
            duration: 0.3,
          });
        },
      });

      // 🌟 Interactive button hover
      button.addEventListener("mouseenter", () => {
        gsap.to(button, {
          scale: 1.05,
          boxShadow: "0 15px 40px rgba(62, 20, 146, 0.4)",
          backgroundColor: "#2D0F6B",
          duration: 0.3,
          ease: "power2.out",
        });

        // Animate arrow
        const arrow = button.querySelector("svg");
        if (arrow) {
          gsap.to(arrow, {
            x: 5,
            rotation: 90,
            duration: 0.3,
            ease: "power2.out",
          });
        }
      });

      button.addEventListener("mouseleave", () => {
        gsap.to(button, {
          scale: 1,
          boxShadow: "0 10px 30px rgba(62, 20, 146, 0.3)",
          backgroundColor: "#3E1492",
          duration: 0.3,
          ease: "power2.out",
        });

        // Reset arrow
        const arrow = button.querySelector("svg");
        if (arrow) {
          gsap.to(arrow, {
            x: 0,
            rotation: 0,
            duration: 0.3,
            ease: "power2.out",
          });
        }
      });
    },
    { scope: platformRef },
  );
  return (
    <div
      ref={platformRef}
      className="max-w-6xl mx-auto py-12 px-4 sm:px-8 md:px-12 lg:px-20 grid grid-cols-1 md:grid-cols-2 gap-10 md:gap-16 items-center my-20"
    >
      {/* image */}
      <div ref={imageRef} className="mb-8 md:mb-0 flex justify-center">
        <Image
          src={platform}
          alt="platform"
          width={530}
          height={500}
          className="rounded-3xl shadow-2xl w-full max-w-xs sm:max-w-md md:max-w-full h-auto"
        />
      </div>

      {/* content */}
      <div ref={contentRef} className="space-y-6">
        <h2
          ref={titleRef}
          className="font-bold text-2xl sm:text-3xl md:text-4xl leading-tight max-w-xl"
        >
          Why Choose <span className="text-[#3E1492]">Sherife Franca</span> Platform?
        </h2>
        <p ref={subtitleRef} className="text-[#475467] text-base sm:text-lg">
          Discover the Benefits That Set Us Apart and Propel Your Fitness Journey Forward.
        </p>
        {/* flex components */}
        <div ref={featuresRef} className="flex flex-col gap-4 mt-6">
          <FlexComponent
            title="Expert Trainers"
            description="Our certified trainers provide personalized guidance and expert advice to help you achieve your fitness goals."
            icon={<CheckCircle2 className="w-6 h-6 text-[#3E1492]" />}
          />
          <FlexComponent
            title="State-of-the-Art Equipment"
            description="Work out with the latest and most advanced fitness equipment to maximize your results and enhance your experience."
            icon={<CheckCircle2 className="w-6 h-6 text-[#3E1492]" />}
          />
          <FlexComponent
            title="Comprehensive Programs"
            description="Enjoy a variety of classes and programs tailored to all fitness levels, from beginner to advanced.."
            icon={<CheckCircle2 className="w-6 h-6 text-[#3E1492]" />}
          />
        </div>
        <button
          ref={buttonRef}
          className="group bg-[#3E1492] text-white px-6 py-3 rounded-md cursor-pointer flex items-center gap-2 mt-6 shadow-md transition-transform"
        >
          Get Start Now
          <ChevronRight className="w-6 h-6 text-white transform transition-transform duration-300 group-hover:rotate-90" />
        </button>
      </div>
    </div>
  );
}
