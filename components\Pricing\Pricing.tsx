"use client";

import { pricingPlans } from "@/data/PricingData";
import { useGSAP } from "@gsap/react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { useRef } from "react";
import PricingCard from "./PricingCard";

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger, useGSAP);

export default function Pricing() {
  const pricingRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const buttonsRef = useRef<HTMLDivElement>(null);
  const cardsContainerRef = useRef<HTMLDivElement>(null);

  // 💎 PRICING SECTION - Diamond Showcase Animations
  useGSAP(
    () => {
      const pricing = pricingRef.current;
      const title = titleRef.current;
      const subtitle = subtitleRef.current;
      const buttons = buttonsRef.current;
      const cardsContainer = cardsContainerRef.current;

      if (!pricing || !title || !subtitle || !buttons || !cardsContainer) return;

      // 💫 Luxury entrance animation on scroll
      ScrollTrigger.create({
        trigger: pricing,
        start: "top 70%",
        end: "bottom 30%",
        onEnter: () => {
          const tl = gsap.timeline();

          // Title emerges like a diamond
          tl.fromTo(
            title,
            {
              scale: 0.5,
              opacity: 0,
              rotationY: 180,
              filter: "brightness(0) contrast(2)",
            },
            {
              scale: 1,
              opacity: 1,
              rotationY: 0,
              filter: "brightness(1) contrast(1)",
              duration: 1.2,
              ease: "back.out(1.7)",
            },
          )
            // Subtitle glides with sparkle
            .fromTo(
              subtitle,
              {
                opacity: 0,
                y: 40,
                filter: "blur(8px)",
              },
              {
                opacity: 1,
                y: 0,
                filter: "blur(0px)",
                duration: 0.8,
                ease: "power2.out",
              },
              "-=0.6",
            )
            // Buttons morph in with glow
            .fromTo(
              buttons.children,
              {
                scale: 0,
                opacity: 0,
                rotationX: -90,
              },
              {
                scale: 1,
                opacity: 1,
                rotationX: 0,
                duration: 0.6,
                stagger: 0.2,
                ease: "elastic.out(1, 0.8)",
              },
              "-=0.4",
            )
            // Cards rise like pedestals
            .fromTo(
              cardsContainer.children,
              {
                y: 100,
                opacity: 0,
                scale: 0.8,
                rotationX: 45,
              },
              {
                y: 0,
                opacity: 1,
                scale: 1,
                rotationX: 0,
                duration: 0.8,
                stagger: {
                  amount: 0.4,
                  from: "center",
                },
                ease: "power3.out",
              },
              "-=0.2",
            );
        },
      });

      // 🎯 Interactive card hover effects
      const cards = cardsContainer.querySelectorAll(".pricing-card");
      cards.forEach((card, index) => {
        card.addEventListener("mouseenter", () => {
          gsap.to(card, {
            scale: 1.08,
            y: -10,
            rotationY: 5,
            boxShadow: "0 25px 50px rgba(62, 20, 146, 0.3)",
            duration: 0.4,
            ease: "power2.out",
          });

          // Highlight effect for other cards
          cards.forEach((otherCard, otherIndex) => {
            if (otherIndex !== index) {
              gsap.to(otherCard, {
                scale: 0.95,
                opacity: 0.7,
                duration: 0.4,
                ease: "power2.out",
              });
            }
          });
        });

        card.addEventListener("mouseleave", () => {
          gsap.to(card, {
            scale: 1,
            y: 0,
            rotationY: 0,
            boxShadow: "0 0 0 rgba(62, 20, 146, 0)",
            duration: 0.4,
            ease: "power2.out",
          });

          // Reset other cards
          cards.forEach(otherCard => {
            gsap.to(otherCard, {
              scale: 1,
              opacity: 1,
              duration: 0.4,
              ease: "power2.out",
            });
          });
        });
      });

      // 🌊 Floating animation for cards
      cards.forEach((card, index) => {
        gsap.to(card, {
          y: "+=10",
          duration: 2 + index * 0.5,
          ease: "sine.inOut",
          yoyo: true,
          repeat: -1,
          delay: index * 0.3,
        });
      });

      // 🎨 Button toggle animation
      const monthlyBtn = buttons.children[0] as HTMLElement;
      const yearlyBtn = buttons.children[1] as HTMLElement;

      if (monthlyBtn && yearlyBtn) {
        monthlyBtn.addEventListener("click", () => {
          gsap.to(monthlyBtn, {
            scale: 1.1,
            backgroundColor: "#3E1492",
            color: "white",
            duration: 0.3,
          });
          gsap.to(yearlyBtn, {
            scale: 1,
            backgroundColor: "transparent",
            color: "#3E1492",
            duration: 0.3,
          });
        });

        yearlyBtn.addEventListener("click", () => {
          gsap.to(yearlyBtn, {
            scale: 1.1,
            backgroundColor: "#3E1492",
            color: "white",
            duration: 0.3,
          });
          gsap.to(monthlyBtn, {
            scale: 1,
            backgroundColor: "transparent",
            color: "#3E1492",
            duration: 0.3,
          });
        });
      }
    },
    { scope: pricingRef },
  );
  return (
    <div ref={pricingRef} className="my-24">
      <h2
        ref={titleRef}
        className="font-bold text-4xl text-center my-6 leading-tight max-w-lg mx-auto"
      >
        Choose the <span className="text-[#3E1492]">Perfect Plan</span> for Your Fitness Journey
      </h2>
      <p ref={subtitleRef} className="text-[ #475467] text-center text-md max-w-md mx-auto">
        Flexible Membership Options to Suit Your Goals and Lifestyle. Find the Right Fit and Start
        Transforming Your Life Today!
      </p>
      <div ref={buttonsRef} className="flex justify-center gap-4 mt-6">
        <button className="bg-[#3E1492] text-white px-6 py-3 rounded-md cursor-pointer">
          Monthly
        </button>
        <button className="text-[#3E1492] border border-[#3E1492] px-6 py-3 rounded-md cursor-pointer">
          Yearly
        </button>
      </div>

      <div
        ref={cardsContainerRef}
        className="max-w-7xl mx-auto grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-10 px-6 place-items-center"
      >
        {pricingPlans.map((plan, idx) => (
          <div key={idx} className="pricing-card">
            <PricingCard
              title={plan.title}
              price={plan.price}
              features={plan.features}
              buttonText={plan.buttonText}
              highlighted={plan.highlighted}
              className={plan.highlighted ? "bg-[#3E1492] text-white scale-105 shadow-2xl" : ""}
            />
          </div>
        ))}
      </div>
    </div>
  );
}
