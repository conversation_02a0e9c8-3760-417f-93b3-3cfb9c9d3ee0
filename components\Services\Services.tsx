"use client";

import { services } from "@/data/Services";

import { useGSAP } from "@gsap/react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { useRef } from "react";
import Card from "./Card";

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger, useGSAP);

export default function Services() {
  const servicesRef = useRef<HTMLElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const cardsContainerRef = useRef<HTMLDivElement>(null);

  // 🎪 SERVICES SECTION - Carnival Entrance Animations
  useGSAP(
    () => {
      const services = servicesRef.current;
      const title = titleRef.current;
      const subtitle = subtitleRef.current;
      const cardsContainer = cardsContainerRef.current;

      if (!services || !title || !subtitle || !cardsContainer) return;

      // 🎠 Carousel entrance animation on scroll
      ScrollTrigger.create({
        trigger: services,
        start: "top 75%",
        end: "bottom 25%",
        onEnter: () => {
          const tl = gsap.timeline();

          // Title spins in like a carnival sign
          tl.fromTo(
            title,
            {
              scale: 0.3,
              rotation: -180,
              opacity: 0,
              filter: "brightness(0) saturate(2)",
            },
            {
              scale: 1,
              rotation: 0,
              opacity: 1,
              filter: "brightness(1) saturate(1)",
              duration: 1,
              ease: "back.out(2)",
            },
          )
            // Subtitle typewriter with glow
            .fromTo(
              subtitle,
              {
                opacity: 0,
                y: 50,
                filter: "blur(10px)",
              },
              {
                opacity: 1,
                y: 0,
                filter: "blur(0px)",
                duration: 0.8,
                ease: "power2.out",
              },
              "-=0.5",
            )
            // Cards fly in from different directions
            .fromTo(
              cardsContainer.children,
              {
                scale: 0,
                opacity: 0,
                rotation: index => (index % 2 === 0 ? -45 : 45),
                y: index => (index % 3 === 0 ? -100 : index % 3 === 1 ? 100 : 0),
                x: index => (index % 3 === 0 ? -50 : index % 3 === 1 ? 50 : 0),
              },
              {
                scale: 1,
                opacity: 1,
                rotation: 0,
                y: 0,
                x: 0,
                duration: 0.8,
                stagger: {
                  amount: 0.6,
                  from: "center",
                  grid: "auto",
                },
                ease: "elastic.out(1, 0.8)",
              },
              "-=0.3",
            );
        },
      });

      // 🎯 Continuous hover effects for cards
      const cards = cardsContainer.querySelectorAll(".service-card");
      cards.forEach((card, index) => {
        card.addEventListener("mouseenter", () => {
          gsap.to(card, {
            scale: 1.05,
            rotationY: 5,
            z: 50,
            boxShadow: "0 20px 40px rgba(62, 20, 146, 0.2)",
            duration: 0.4,
            ease: "power2.out",
          });
        });

        card.addEventListener("mouseleave", () => {
          gsap.to(card, {
            scale: 1,
            rotationY: 0,
            z: 0,
            boxShadow: "0 0 0 rgba(62, 20, 146, 0)",
            duration: 0.4,
            ease: "power2.out",
          });
        });
      });

      // 🌊 Parallax scroll effect
      ScrollTrigger.create({
        trigger: services,
        start: "top bottom",
        end: "bottom top",
        scrub: 1,
        onUpdate: self => {
          const progress = self.progress;

          // Cards move at different speeds
          cards.forEach((card, index) => {
            const speed = ((index % 3) + 1) * 0.5;
            gsap.to(card, {
              y: progress * speed * 30,
              rotation: progress * (index % 2 === 0 ? 2 : -2),
              duration: 0.3,
            });
          });
        },
      });
    },
    { scope: servicesRef },
  );
  return (
    <section ref={servicesRef}>
      <div className="max-w-7xl mx-auto py-12 px-4 sm:px-8 md:px-12 lg:px-20">
        <h2 ref={titleRef} className="text-5xl font-bold text-center">
          Premium <span className="text-[#3E1492]">Fitness Services</span>
        </h2>
        <p ref={subtitleRef} className="text-center text-lg text-[#475467]">
          Tailored Workouts, Expert Guidance, and Comprehensive Programs to Meet All Your Fitness
          Needs
        </p>
        <div className="w-full flex justify-center">
          <div
            ref={cardsContainerRef}
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 mt-10 w-full"
          >
            {services.map((service, idx) => (
              <div key={idx} className="flex justify-center h-full service-card">
                <Card
                  title={service.title}
                  description={service.description}
                  image={service.image}
                />
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
