"use client";

import StoriesImage from "@/public/bigpic.jpg";
import smallpic1 from "@/public/smpic1.jpg";
import smallpic2 from "@/public/smpic2.jpg";
import { useGSAP } from "@gsap/react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import Image from "next/image";
import { useRef } from "react";

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger, useGSAP);

export default function Stories() {
  const storiesRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const imagesRef = useRef<HTMLDivElement>(null);
  const mainImageRef = useRef<HTMLImageElement>(null);
  const smallImage1Ref = useRef<HTMLImageElement>(null);
  const smallImage2Ref = useRef<HTMLImageElement>(null);

  // 📖 STORIES SECTION - Magazine Layout Animations
  useGSAP(
    () => {
      const stories = storiesRef.current;
      const content = contentRef.current;
      const title = titleRef.current;
      const subtitle = subtitleRef.current;
      const button = buttonRef.current;
      const images = imagesRef.current;
      const mainImage = mainImageRef.current;
      const smallImage1 = smallImage1Ref.current;
      const smallImage2 = smallImage2Ref.current;

      if (
        !stories ||
        !content ||
        !title ||
        !subtitle ||
        !button ||
        !images ||
        !mainImage ||
        !smallImage1 ||
        !smallImage2
      )
        return;

      // 📚 Magazine-style entrance animation on scroll
      ScrollTrigger.create({
        trigger: stories,
        start: "top 75%",
        end: "bottom 25%",
        onEnter: () => {
          const tl = gsap.timeline();

          // Content slides in like turning a page
          tl.fromTo(
            content,
            {
              x: -100,
              opacity: 0,
              rotationY: -15,
              transformOrigin: "right center",
            },
            {
              x: 0,
              opacity: 1,
              rotationY: 0,
              duration: 1,
              ease: "power3.out",
            },
          )
            // Title unfolds like a headline
            .fromTo(
              title,
              {
                opacity: 0,
                y: 30,
                scaleY: 0,
                transformOrigin: "top center",
              },
              {
                opacity: 1,
                y: 0,
                scaleY: 1,
                duration: 0.8,
                ease: "back.out(1.7)",
              },
              "-=0.7",
            )
            // Subtitle types in
            .fromTo(
              subtitle,
              {
                opacity: 0,
                filter: "blur(5px)",
              },
              {
                opacity: 1,
                filter: "blur(0px)",
                duration: 0.8,
                ease: "power2.out",
              },
              "-=0.4",
            )
            // Button pops in
            .fromTo(
              button,
              {
                scale: 0,
                opacity: 0,
                rotation: -10,
              },
              {
                scale: 1,
                opacity: 1,
                rotation: 0,
                duration: 0.6,
                ease: "elastic.out(1, 0.8)",
              },
              "-=0.3",
            )
            // Main image slides in with depth
            .fromTo(
              mainImage,
              {
                scale: 0.8,
                opacity: 0,
                rotationY: 20,
                z: -100,
              },
              {
                scale: 1,
                opacity: 1,
                rotationY: 0,
                z: 0,
                duration: 1,
                ease: "power3.out",
              },
              "-=0.8",
            )
            // Small images fly in from corners
            .fromTo(
              smallImage1,
              {
                x: -150,
                y: 100,
                scale: 0.3,
                opacity: 0,
                rotation: -45,
              },
              {
                x: 0,
                y: 0,
                scale: 1,
                opacity: 1,
                rotation: 0,
                duration: 0.8,
                ease: "back.out(1.7)",
              },
              "-=0.6",
            )
            .fromTo(
              smallImage2,
              {
                x: 150,
                y: -100,
                scale: 0.3,
                opacity: 0,
                rotation: 45,
              },
              {
                x: 0,
                y: 0,
                scale: 1,
                opacity: 1,
                rotation: 0,
                duration: 0.8,
                ease: "back.out(1.7)",
              },
              "-=0.4",
            );
        },
      });

      // 🎯 Continuous floating animations
      gsap.to(mainImage, {
        y: "+=15",
        rotation: "+=1",
        duration: 4,
        ease: "sine.inOut",
        yoyo: true,
        repeat: -1,
      });

      gsap.to(smallImage1, {
        y: "+=10",
        x: "+=5",
        rotation: "+=2",
        duration: 3,
        ease: "sine.inOut",
        yoyo: true,
        repeat: -1,
        delay: 0.5,
      });

      gsap.to(smallImage2, {
        y: "+=12",
        x: "+=3",
        rotation: "+=1.5",
        duration: 3.5,
        ease: "sine.inOut",
        yoyo: true,
        repeat: -1,
        delay: 1,
      });

      // 🌊 Parallax scroll effect
      ScrollTrigger.create({
        trigger: stories,
        start: "top bottom",
        end: "bottom top",
        scrub: 1,
        onUpdate: self => {
          const progress = self.progress;

          // Images move at different speeds
          gsap.to(mainImage, {
            y: progress * 30,
            duration: 0.3,
          });

          gsap.to(smallImage1, {
            y: progress * -20,
            x: progress * 10,
            duration: 0.3,
          });

          gsap.to(smallImage2, {
            y: progress * 40,
            x: progress * -15,
            duration: 0.3,
          });
        },
      });

      // 🎨 Interactive button hover
      button.addEventListener("mouseenter", () => {
        gsap.to(button, {
          scale: 1.05,
          boxShadow: "0 15px 35px rgba(62, 20, 146, 0.4)",
          backgroundColor: "#2D0F6B",
          duration: 0.3,
          ease: "power2.out",
        });
      });

      button.addEventListener("mouseleave", () => {
        gsap.to(button, {
          scale: 1,
          boxShadow: "0 0 0 rgba(62, 20, 146, 0)",
          backgroundColor: "#3E1492",
          duration: 0.3,
          ease: "power2.out",
        });
      });
    },
    { scope: storiesRef },
  );
  return (
    <div
      ref={storiesRef}
      className="max-w-7xl mx-auto py-12 px-4 sm:px-8 md:px-12 lg:px-20 grid grid-cols-1 md:grid-cols-2 gap-10 md:gap-16 items-center my-20 place-items-center"
    >
      {/* content */}
      <div ref={contentRef} className="space-y-4">
        <h2 ref={titleRef} className="text-4xl font-bold leading-tight max-w-[458px]">
          Inspiring Success Stories from <span className="text-[#3E1492]">Sherife Franca</span>{" "}
          Platform
        </h2>
        <p ref={subtitleRef} className="text-lg text-[#475467] max-w-[458px]">
          Sherife Franca Platform, we celebrate the incredible journeys of our members. From weight
          loss triumphs and muscle gain milestones to enhanced well-being and newfound confidence,
          our members&apos; success stories highlight the power of commitment and community. Explore
          these inspiring testimonials and see how FitLife Studio can help you reach your fitness
          goals.
        </p>
        <button
          ref={buttonRef}
          className="bg-[#3E1492] text-white px-4 py-2 rounded-md cursor-pointer flex items-center gap-2 mt-4"
        >
          Join Today
        </button>
      </div>
      {/* image */}
      <div ref={imagesRef} className="relative">
        <Image
          ref={mainImageRef}
          src={StoriesImage}
          alt="stories"
          width={487}
          height={532}
          className="rounded-3xl shadow-2xl "
        />
        <Image
          ref={smallImage1Ref}
          src={smallpic1}
          alt="stories"
          width={216}
          height={216}
          className="rounded-3xl shadow-2xl absolute top-[230px] -left-[30px]"
        />
        <Image
          ref={smallImage2Ref}
          src={smallpic2}
          alt="stories"
          width={244}
          height={300}
          className="rounded-3xl shadow-xl absolute top-[64px] -right-[50px]"
        />
      </div>
    </div>
  );
}
