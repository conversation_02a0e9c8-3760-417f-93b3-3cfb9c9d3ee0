import john from "@/public/john.jpg";
import mike from "@/public/mike.jpg";
import sarah from "@/public/sarah.jpg";
import { useGSAP } from "@gsap/react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import Image from "next/image";
import { useRef } from "react";
import { FaStar } from "react-icons/fa";

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger, useGSAP);

export default function Transformation() {
  const transformationRef = useRef<HTMLElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const cardsContainerRef = useRef<HTMLDivElement>(null);

  // ⭐ TRANSFORMATION - Star-Studded Testimonial Showcase
  useGSAP(
    () => {
      const transformation = transformationRef.current;
      const title = titleRef.current;
      const subtitle = subtitleRef.current;
      const cardsContainer = cardsContainerRef.current;

      if (!transformation || !title || !subtitle || !cardsContainer) return;

      // 🌟 Scroll-triggered star entrance
      ScrollTrigger.create({
        trigger: transformation,
        start: "top 70%",
        end: "bottom 30%",
        onEnter: () => {
          const tl = gsap.timeline();

          // Title shimmers in like stars
          tl.fromTo(
            title,
            {
              opacity: 0,
              scale: 0.8,
              filter: "brightness(0) saturate(2)",
              textShadow: "0 0 0 rgba(62, 20, 146, 0)",
            },
            {
              opacity: 1,
              scale: 1,
              filter: "brightness(1) saturate(1)",
              textShadow: "0 0 20px rgba(62, 20, 146, 0.3)",
              duration: 1,
              ease: "power3.out",
            },
          )
            // Subtitle fades with glow
            .fromTo(
              subtitle,
              {
                opacity: 0,
                y: 30,
                filter: "blur(5px)",
              },
              {
                opacity: 1,
                y: 0,
                filter: "blur(0px)",
                duration: 0.8,
                ease: "power2.out",
              },
              "-=0.5",
            )
            // Cards rise like testimonials coming to life
            .fromTo(
              cardsContainer.children,
              {
                y: 80,
                opacity: 0,
                scale: 0.9,
                rotationX: 30,
              },
              {
                y: 0,
                opacity: 1,
                scale: 1,
                rotationX: 0,
                duration: 0.8,
                stagger: {
                  amount: 0.6,
                  from: "start",
                },
                ease: "back.out(1.7)",
              },
              "-=0.3",
            );
        },
      });

      // 🎯 Interactive card hover effects
      const cards = cardsContainer.querySelectorAll(".testimonial-card");
      cards.forEach((card, index) => {
        const stars = card.querySelectorAll(".star-icon");

        card.addEventListener("mouseenter", () => {
          gsap.to(card, {
            scale: 1.05,
            y: -10,
            boxShadow: "0 20px 40px rgba(62, 20, 146, 0.2)",
            borderColor: "#3E1492",
            duration: 0.4,
            ease: "power2.out",
          });

          // Animate stars
          gsap.to(stars, {
            scale: 1.2,
            rotation: 360,
            color: "#FFD700",
            duration: 0.3,
            stagger: 0.05,
            ease: "power2.out",
          });
        });

        card.addEventListener("mouseleave", () => {
          gsap.to(card, {
            scale: 1,
            y: 0,
            boxShadow: "0 0 0 rgba(62, 20, 146, 0)",
            borderColor: "#3E1492",
            duration: 0.4,
            ease: "power2.out",
          });

          // Reset stars
          gsap.to(stars, {
            scale: 1,
            rotation: 0,
            color: "#3E1492",
            duration: 0.3,
            stagger: 0.05,
            ease: "power2.out",
          });
        });
      });

      // 🌊 Floating animation for cards
      cards.forEach((card, index) => {
        gsap.to(card, {
          y: "+=8",
          duration: 2.5 + index * 0.3,
          ease: "sine.inOut",
          yoyo: true,
          repeat: -1,
          delay: index * 0.2,
        });
      });
    },
    { scope: transformationRef },
  );
  return (
    <section ref={transformationRef} className="my-32">
      <h2 ref={titleRef} className="text-4xl font-bold text-center my-6 leading-tight  mx-auto">
        Real Voices, <span className="text-[#3E1492]">Real Transformations</span>
      </h2>
      <p
        ref={subtitleRef}
        className="text-center text-lg max-w-[862px] mx-auto text-[#475467] px-4 md:px-0"
      >
        FitLife Studio’s group classes are so much fun and motivating. I have lost 20 pounds and
        gained a ton of confidence. The community here is amazing!.
      </p>

      <div
        ref={cardsContainerRef}
        className=" max-w-7xl mx-auto grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 mt-12 place-items-center gap-8 "
      >
        {/* card 1 */}
        <div className="max-w-[360px] h-[350px] p-6 rounded-xl border border-[#3E1492] flex flex-col space-y-8 testimonial-card transition-all duration-300">
          <div className="flex items-center gap-2">
            <FaStar className="text-[#3E1492] text-xl star-icon" />
            <FaStar className="text-[#3E1492] text-xl star-icon" />
            <FaStar className="text-[#3E1492] text-xl star-icon" />
            <FaStar className="text-[#3E1492] text-xl star-icon" />
            <FaStar className="text-[#3E1492] text-xl star-icon" />
          </div>
          <p className="max-w-[390px]">
            Joining FitLife Studio was the best decision I ever made for my health. The trainers are
            incredibly supportive, and the personalized programs have helped me achieve results I{" "}
          </p>
          <div className="flex items-center gap-4">
            <Image
              src={john}
              alt="john"
              width={64}
              height={64}
              className="w-16 h-16 rounded-full object-cover border-2 border-[#3E1492] shadow"
            />
            <div>
              <h3 className="font-bold text-lg">John Doe</h3>
              <p className="text-[#475467]">Member Since 2021</p>
            </div>
          </div>
        </div>
        {/* card 2 */}
        <div className="max-w-[360px] h-[350px] p-6 rounded-xl border border-[#3E1492] flex flex-col space-y-8 testimonial-card transition-all duration-300">
          <div className="flex items-center gap-2">
            <FaStar className="text-[#3E1492] text-xl star-icon" />
            <FaStar className="text-[#3E1492] text-xl star-icon" />
            <FaStar className="text-[#3E1492] text-xl star-icon" />
            <FaStar className="text-[#3E1492] text-xl star-icon" />
            <FaStar className="text-[#3E1492] text-xl star-icon" />
          </div>
          <p className="max-w-[390px]">
            Joining FitLife Studio was the best decision I ever made for my health. The trainers are
            incredibly supportive, and the personalized programs have helped me achieve results I{" "}
          </p>
          <div className="flex items-center gap-4">
            <Image
              src={sarah}
              alt="john"
              width={64}
              height={64}
              className="w-16 h-16 rounded-full object-cover border-2 border-[#3E1492] shadow"
            />
            <div>
              <h3 className="font-bold text-lg">Sarah Smith</h3>
              <p className="text-[#475467]">Graphic Designer</p>
            </div>
          </div>
        </div>
        {/* card 3 */}

        <div className="max-w-[360px] h-[350px] p-6 rounded-xl border border-[#3E1492] flex flex-col space-y-8 testimonial-card transition-all duration-300">
          <div className="flex items-center gap-2">
            <FaStar className="text-[#3E1492] text-xl star-icon" />
            <FaStar className="text-[#3E1492] text-xl star-icon" />
            <FaStar className="text-[#3E1492] text-xl star-icon" />
            <FaStar className="text-[#3E1492] text-xl star-icon" />
            <FaStar className="text-[#3E1492] text-xl star-icon" />
          </div>
          <p className="max-w-[390px]">
            The holistic approach at FitLife Studio has improved my overall well-being. The
            combination of strength training, cardio, and wellness programs has been life-changing.{" "}
          </p>
          <div className="flex items-center gap-4">
            <Image
              src={mike}
              alt="john"
              width={64}
              height={64}
              className="w-16 h-16 rounded-full object-cover border-2 border-[#3E1492] shadow"
            />
            <div>
              <h3 className="font-bold text-lg">Mike Johnson</h3>
              <p className="text-[#475467]">Entrepreneur</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
