import { cn } from "@/lib/utils";

interface ContactInfoProps {
  className?: string;
}

interface ContactCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  contact: string;
  className?: string;
}

function ContactCard({ icon, title, description, contact, className }: ContactCardProps) {
  return (
    <div className={cn(
      "bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform  border border-[#3E1492]",
      className
    )}>
      <div className="flex flex-col items-center text-center space-y-4">
        <div className="w-16 h-16 bg-gradient-to-r from-[#3E1492] to-[#6B46C1] rounded-full flex items-center justify-center text-white">
          {icon}
        </div>
        <div>
          <h3 className="text-xl font-bold text-gray-900 mb-2">{title}</h3>
          <p className="text-gray-600 mb-3">{description}</p>
          <p className="text-[#3E1492] font-semibold">{contact}</p>
        </div>
      </div>
    </div>
  );
}

export default function ContactInfo({ className }: ContactInfoProps) {
  return (
    <div className={cn("w-full", className)}>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Email Card */}
        <ContactCard
          icon={
            <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          }
          title="Email"
          description="For Inquiries and Support, Reach to Us"
          contact="<EMAIL>"
        />

        {/* Live Chat Card */}
        <ContactCard
          icon={
            <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 3.582-8 8-8s8 3.582 8 8z" />
            </svg>
          }
          title="Live Chat"
          description="Chat with Us - Immediate and Support"
          contact="(+20) 0100000654"
        />

        {/* Phone Card */}
        <ContactCard
          icon={
            <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
            </svg>
          }
          title="Phone"
          description="Speak with Our Team Directly"
          contact="(+20) 0100000654"
        />
      </div>
    </div>
  );
}
