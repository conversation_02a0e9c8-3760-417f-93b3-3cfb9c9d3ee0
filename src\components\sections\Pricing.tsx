import { Section } from "@/components/layout";
import { Button, PricingCard } from "@/components/ui";
import { pricingPlans } from "@/data";
import { cn } from "@/lib/utils";

export default function Pricing() {
  return (
    <Section id="pricing" className="overflow-x-hidden">
      <div className="text-center mb-12">
        <h2 className="font-bold text-4xl mb-6 leading-tight max-w-4xl mx-auto">
          Choose the <span className="text-[#3E1492]">Perfect Plan</span> for Your Fitness Journey
        </h2>
        <p className="text-[#475467] text-lg max-w-2xl mx-auto leading-relaxed">
          Flexible Membership Options to Suit Your Goals and Lifestyle. Find the Right Fit and Start
          Transforming Your Life Today!
        </p>
      </div>

      {/* Toggle Buttons */}
      <div className="flex justify-center gap-4 mb-12">
        <Button variant="primary" size="lg">
          Monthly
        </Button>
        <Button variant="outline" size="lg">
          Yearly
        </Button>
      </div>

      {/* Pricing Cards Grid */}
      <div className="w-full overflow-x-hidden">
        <div className="pricing-grid">
          {pricingPlans.map((plan, idx) => (
            <div key={idx} className="flex justify-center w-full">
              <PricingCard
                title={plan.title}
                price={plan.price}
                features={plan.features}
                buttonText={plan.buttonText}
                highlighted={plan.isPopular}
                className={cn(
                  "w-full",
                  plan.isPopular ? "bg-[#3E1492] text-white scale-105 shadow-2xl" : "",
                )}
              />
            </div>
          ))}
        </div>
      </div>
    </Section>
  );
}
