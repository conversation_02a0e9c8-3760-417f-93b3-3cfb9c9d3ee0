"use client";

import { Section } from "@/components/layout";
import ServiceCard from "@/components/ui/ServiceCard";
import { services } from "@/data";
import { useGSAP } from "@gsap/react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { useRef } from "react";

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger, useGSAP);

export default function Services() {
  const servicesRef = useRef<HTMLElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const cardsContainerRef = useRef<HTMLDivElement>(null);

  // 🎪 SERVICES - Carnival Entrance Animations
  useGSAP(
    () => {
      const services = servicesRef.current;
      const title = titleRef.current;
      const subtitle = subtitleRef.current;
      const cardsContainer = cardsContainerRef.current;

      if (!services || !title || !subtitle || !cardsContainer) return;

      // 🌟 Scroll-triggered carnival entrance
      ScrollTrigger.create({
        trigger: services,
        start: "top 70%",
        end: "bottom 30%",
        onEnter: () => {
          const tl = gsap.timeline();

          // Title spins in like a carnival sign
          tl.fromTo(
            title,
            {
              opacity: 0,
              scale: 0.5,
              rotation: -180,
              filter: "brightness(0) contrast(2)",
              textShadow: "0 0 0 rgba(62, 20, 146, 0)",
            },
            {
              opacity: 1,
              scale: 1,
              rotation: 0,
              filter: "brightness(1) contrast(1)",
              textShadow: "0 0 30px rgba(62, 20, 146, 0.4)",
              duration: 1.2,
              ease: "back.out(1.7)",
            },
          )
            // Subtitle fades in with glow
            .fromTo(
              subtitle,
              {
                opacity: 0,
                y: 50,
                filter: "blur(8px)",
              },
              {
                opacity: 1,
                y: 0,
                filter: "blur(0px)",
                duration: 0.8,
                ease: "power2.out",
              },
              "-=0.6",
            )
            // Cards fly in from different directions
            .fromTo(
              cardsContainer.children,
              {
                opacity: 0,
                scale: 0.3,
                y: index => {
                  // Different directions for each card
                  const directions = [100, -100, 100]; // up, down, up pattern
                  return directions[index % 3];
                },
                x: index => {
                  // Different horizontal positions
                  const positions = [-150, 0, 150]; // left, center, right
                  return positions[index % 3];
                },
                rotation: index => {
                  // Different rotations
                  const rotations = [-45, 45, -45];
                  return rotations[index % 3];
                },
                filter: "brightness(0)",
              },
              {
                opacity: 1,
                scale: 1,
                y: 0,
                x: 0,
                rotation: 0,
                filter: "brightness(1)",
                duration: 1,
                stagger: {
                  amount: 0.8,
                  from: "start",
                },
                ease: "elastic.out(1, 0.8)",
              },
              "-=0.4",
            );
        },
      });

      // 🎯 Interactive card hover effects
      const cards = cardsContainer.querySelectorAll(".service-card");
      cards.forEach((card, index) => {
        card.addEventListener("mouseenter", () => {
          // Spotlight effect - dim other cards
          cards.forEach((otherCard, otherIndex) => {
            if (otherIndex !== index) {
              gsap.to(otherCard, {
                opacity: 0.3,
                scale: 0.95,
                filter: "blur(2px)",
                duration: 0.4,
                ease: "power2.out",
              });
            }
          });

          // Highlight current card
          gsap.to(card, {
            scale: 1.08,
            y: -15,
            rotationY: 5,
            boxShadow: "0 25px 60px rgba(62, 20, 146, 0.3)",
            filter: "brightness(1.1) saturate(1.2)",
            duration: 0.4,
            ease: "power2.out",
          });
        });

        card.addEventListener("mouseleave", () => {
          // Reset all cards
          cards.forEach(otherCard => {
            gsap.to(otherCard, {
              opacity: 1,
              scale: 1,
              filter: "blur(0px)",
              duration: 0.4,
              ease: "power2.out",
            });
          });

          // Reset current card
          gsap.to(card, {
            scale: 1,
            y: 0,
            rotationY: 0,
            boxShadow: "0 0 0 rgba(62, 20, 146, 0)",
            filter: "brightness(1) saturate(1)",
            duration: 0.4,
            ease: "power2.out",
          });
        });
      });

      // 🎨 Parallax scroll effect
      ScrollTrigger.create({
        trigger: services,
        start: "top bottom",
        end: "bottom top",
        scrub: 1,
        onUpdate: self => {
          const progress = self.progress;

          // Cards move at different speeds for depth
          cards.forEach((card, index) => {
            const speed = (index % 2 === 0 ? 1 : -1) * 0.5;
            gsap.to(card, {
              y: progress * speed * 40,
              rotation: progress * (index % 2 === 0 ? 2 : -2),
              duration: 0.3,
            });
          });
        },
      });

      // 🌊 Continuous floating animations
      cards.forEach((card, index) => {
        gsap.to(card, {
          y: "+=10",
          rotation: "+=1",
          duration: 3 + index * 0.5,
          ease: "sine.inOut",
          yoyo: true,
          repeat: -1,
          delay: index * 0.3,
        });
      });
    },
    { scope: servicesRef },
  );

  return (
    <Section ref={servicesRef} id="services">
      <div className="text-center mb-12">
        <h2 ref={titleRef} className="text-5xl font-bold mb-4">
          Premium <span className="text-[#3E1492]">Fitness Services</span>
        </h2>
        <p ref={subtitleRef} className="text-lg text-[#475467] max-w-3xl mx-auto">
          Tailored Workouts, Expert Guidance, and Comprehensive Programs to Meet All Your Fitness
          Needs
        </p>
      </div>

      <div ref={cardsContainerRef} className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
        {services.map((service, idx) => (
          <div key={idx} className="flex justify-center h-full service-card">
            <ServiceCard
              title={service.title}
              description={service.description}
              image={service.image}
            />
          </div>
        ))}
      </div>
    </Section>
  );
}
