import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { ANIMATION_DURATIONS, ANIMATION_EASES } from "@/constants";
import type { AnimationConfig, ScrollTriggerConfig } from "@/types";

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger);

// ============================================================================
// ANIMATION UTILITIES
// ============================================================================

/**
 * Default animation configuration
 */
export const DEFAULT_ANIMATION: AnimationConfig = {
  duration: ANIMATION_DURATIONS.normal,
  ease: ANIMATION_EASES.power2,
  delay: 0,
  stagger: 0.1
};

/**
 * Creates a fade in animation
 */
export function fadeIn(
  element: gsap.TweenTarget,
  config: Partial<AnimationConfig> = {}
) {
  const { duration, ease, delay } = { ...DEFAULT_ANIMATION, ...config };
  
  return gsap.fromTo(
    element,
    { opacity: 0, y: 30 },
    { opacity: 1, y: 0, duration, ease, delay }
  );
}

/**
 * Creates a fade in up animation
 */
export function fadeInUp(
  element: gsap.TweenTarget,
  config: Partial<AnimationConfig> = {}
) {
  const { duration, ease, delay } = { ...DEFAULT_ANIMATION, ...config };
  
  return gsap.fromTo(
    element,
    { opacity: 0, y: 50 },
    { opacity: 1, y: 0, duration, ease, delay }
  );
}

/**
 * Creates a scale in animation
 */
export function scaleIn(
  element: gsap.TweenTarget,
  config: Partial<AnimationConfig> = {}
) {
  const { duration, ease, delay } = { ...DEFAULT_ANIMATION, ...config };
  
  return gsap.fromTo(
    element,
    { opacity: 0, scale: 0.8 },
    { opacity: 1, scale: 1, duration, ease, delay }
  );
}

/**
 * Creates a slide in from left animation
 */
export function slideInLeft(
  element: gsap.TweenTarget,
  config: Partial<AnimationConfig> = {}
) {
  const { duration, ease, delay } = { ...DEFAULT_ANIMATION, ...config };
  
  return gsap.fromTo(
    element,
    { opacity: 0, x: -100 },
    { opacity: 1, x: 0, duration, ease, delay }
  );
}

/**
 * Creates a slide in from right animation
 */
export function slideInRight(
  element: gsap.TweenTarget,
  config: Partial<AnimationConfig> = {}
) {
  const { duration, ease, delay } = { ...DEFAULT_ANIMATION, ...config };
  
  return gsap.fromTo(
    element,
    { opacity: 0, x: 100 },
    { opacity: 1, x: 0, duration, ease, delay }
  );
}

/**
 * Creates a staggered animation for multiple elements
 */
export function staggerAnimation(
  elements: gsap.TweenTarget,
  animation: gsap.TweenVars,
  stagger: number = 0.1
) {
  return gsap.fromTo(
    elements,
    animation.from || { opacity: 0, y: 30 },
    { 
      ...animation.to,
      stagger: { amount: stagger, from: "start" }
    }
  );
}

/**
 * Creates a scroll-triggered animation
 */
export function createScrollTrigger(
  element: gsap.TweenTarget,
  animation: gsap.TweenVars,
  config: Partial<ScrollTriggerConfig> = {}
) {
  const { trigger, start, end, scrub } = {
    trigger: element,
    start: "top 80%",
    end: "bottom 20%",
    scrub: false,
    ...config
  };

  return ScrollTrigger.create({
    trigger,
    start,
    end,
    scrub,
    animation: gsap.timeline().add(animation)
  });
}

/**
 * Creates a parallax scroll effect
 */
export function createParallax(
  element: gsap.TweenTarget,
  speed: number = 0.5
) {
  return ScrollTrigger.create({
    trigger: element,
    start: "top bottom",
    end: "bottom top",
    scrub: true,
    onUpdate: (self) => {
      const progress = self.progress;
      gsap.to(element, {
        y: progress * speed * 100,
        duration: 0.3
      });
    }
  });
}
